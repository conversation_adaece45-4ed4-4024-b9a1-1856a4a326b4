{"name": "tanstack-start-example-supabase-basic", "version": "1.0.0", "description": "", "main": "index.js", "type": "module", "scripts": {"dev": "vinxi dev", "build": "vinxi build", "start": "vinxi start"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@hookform/resolvers": "^5.0.1", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@supabase/ssr": "^0.5.2", "@supabase/supabase-js": "^2.48.1", "@tanstack/react-query": "^5.77.0", "@tanstack/react-router": "^1.120.10", "@tanstack/react-router-devtools": "^1.120.10", "@tanstack/react-start": "^1.120.10", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "framer-motion": "^12.12.2", "lucide-react": "^0.511.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.56.4", "tailwind-merge": "^3.3.0", "tailwindcss-animate": "^1.0.7", "vinxi": "0.5.3", "zod": "^3.25.28", "zustand": "^5.0.5"}, "devDependencies": {"@types/react": "^19.0.8", "@types/react-dom": "^19.0.3", "autoprefixer": "^10.4.20", "postcss": "^8.5.1", "tailwindcss": "^3.4.17", "typescript": "^5.7.2", "vite-tsconfig-paths": "^5.1.4"}}