import { Link } from '@tanstack/react-router'
import { Clock, User } from 'lucide-react'
import { Button } from './ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from './ui/dropdown-menu'
import {
  Too<PERSON><PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from './ui/tooltip'
import { Badge } from './ui/badge'
import { SimpleThemeToggle } from './simple-theme-toggle'

interface NavigationProps {
  user?: { email: string } | null
}

export function Navigation({ user }: NavigationProps) {
  return (
    <TooltipProvider>
      <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="container flex h-16 items-center">
          {/* Logo and Brand */}
          <Tooltip>
            <TooltipTrigger asChild>
              <Link to="/" className="flex items-center space-x-3 mr-6 group">
                <div className="w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center group-hover:scale-105 transition-transform">
                  <Clock className="w-5 h-5 text-white" />
                </div>
                <div className="hidden sm:block">
                  <h1 className="text-xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                    Timeline Creator
                  </h1>
                  <Badge variant="secondary" className="text-xs ml-2">
                    Beta
                  </Badge>
                </div>
              </Link>
            </TooltipTrigger>
            <TooltipContent>
              <p>Go to homepage</p>
            </TooltipContent>
          </Tooltip>

        {/* Main Navigation */}
        <nav className="flex items-center space-x-6 text-sm font-medium">
          <Link
            to="/"
            className="transition-colors hover:text-foreground/80 text-foreground/60"
            activeProps={{
              className: 'text-foreground',
            }}
            activeOptions={{ exact: true }}
          >
            Home
          </Link>
          <Link
            to="/posts"
            className="transition-colors hover:text-foreground/80 text-foreground/60"
            activeProps={{
              className: 'text-foreground',
            }}
          >
            Posts
          </Link>
          {user && (
            <Link
              to="/timeline"
              className="transition-colors hover:text-foreground/80 text-foreground/60 font-medium"
              activeProps={{
                className: 'text-foreground',
              }}
            >
              Timeline Creator
            </Link>
          )}
        </nav>

        {/* Right side actions */}
        <div className="ml-auto flex items-center space-x-4">
          <Tooltip>
            <TooltipTrigger asChild>
              <div>
                <SimpleThemeToggle />
              </div>
            </TooltipTrigger>
            <TooltipContent>
              <p>Toggle theme</p>
            </TooltipContent>
          </Tooltip>

          {user ? (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm" className="relative h-8 w-8 rounded-full">
                  <User className="h-4 w-4" />
                  <span className="sr-only">Open user menu</span>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-56" align="end" forceMount>
                <div className="flex items-center justify-start gap-2 p-2">
                  <div className="flex flex-col space-y-1 leading-none">
                    <p className="font-medium">{user.email}</p>
                  </div>
                </div>
                <DropdownMenuSeparator />
                <DropdownMenuItem asChild>
                  <Link to="/logout" className="w-full cursor-pointer">
                    Log out
                  </Link>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          ) : (
            <div className="flex items-center space-x-2">
              <Button variant="ghost" size="sm" asChild>
                <Link to="/login">Sign in</Link>
              </Button>
              <Button size="sm" asChild>
                <Link to="/signup">Sign up</Link>
              </Button>
            </div>
          )}
          </div>
        </div>
      </header>
    </TooltipProvider>
  )
}
