import { createFileRoute } from '@tanstack/react-router'
import { But<PERSON> } from '~/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '~/components/ui/card'
import { Badge } from '~/components/ui/badge'
import { Separator } from '~/components/ui/separator'
import { Plus, Calendar, Clock, Users, Zap } from 'lucide-react'

export const Route = createFileRoute('/')({
  component: Home,
})

function Home() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-background to-muted/20">
      {/* Hero Section */}
      <section className="container mx-auto px-4 py-16">
        <div className="text-center max-w-4xl mx-auto">
          <h2 className="text-4xl md:text-6xl font-bold mb-6 bg-gradient-to-r from-blue-600 via-purple-600 to-blue-800 bg-clip-text text-transparent">
            Create Beautiful Timelines
          </h2>
          <p className="text-xl text-muted-foreground mb-8 leading-relaxed">
            Transform your ideas into stunning visual timelines. Perfect for project planning,
            storytelling, and organizing information chronologically with Miro-inspired design.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700">
              <Plus className="w-5 h-5 mr-2" />
              Create Your First Timeline
            </Button>
            <Button variant="outline" size="lg">
              <Calendar className="w-5 h-5 mr-2" />
              View Examples
            </Button>
          </div>
        </div>
      </section>

      {/* Features Grid */}
      <section className="container mx-auto px-4 py-16">
        <div className="text-center mb-12">
          <h3 className="text-3xl font-bold mb-4">Why Choose Timeline Creator?</h3>
          <p className="text-muted-foreground max-w-2xl mx-auto">
            Built with modern technologies and Apple's design principles for the best user experience.
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
          <Card className="hover:shadow-lg transition-all duration-300 hover:-translate-y-1">
            <CardHeader>
              <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mb-4">
                <Zap className="w-6 h-6 text-primary" />
              </div>
              <CardTitle>Lightning Fast</CardTitle>
              <CardDescription>
                Built with TanStack Start and optimized for performance with sub-100ms interactions.
              </CardDescription>
            </CardHeader>
          </Card>

          <Card className="hover:shadow-lg transition-all duration-300 hover:-translate-y-1">
            <CardHeader>
              <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mb-4">
                <Users className="w-6 h-6 text-primary" />
              </div>
              <CardTitle>Real-time Collaboration</CardTitle>
              <CardDescription>
                Work together with your team in real-time with Supabase-powered live updates.
              </CardDescription>
            </CardHeader>
          </Card>

          <Card className="hover:shadow-lg transition-all duration-300 hover:-translate-y-1">
            <CardHeader>
              <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mb-4">
                <Calendar className="w-6 h-6 text-primary" />
              </div>
              <CardTitle>Intuitive Design</CardTitle>
              <CardDescription>
                Miro-inspired interface with Apple's animation principles for delightful interactions.
              </CardDescription>
            </CardHeader>
          </Card>
        </div>
      </section>

      {/* Timeline Preview */}
      <section className="container mx-auto px-4 py-16">
        <div className="text-center mb-12">
          <h3 className="text-3xl font-bold mb-4">See It In Action</h3>
          <p className="text-muted-foreground">A glimpse of what your timelines will look like</p>
        </div>

        <Card className="max-w-4xl mx-auto shadow-lg overflow-hidden">
          <CardContent className="p-8">
            <div className="relative">
              {/* Timeline Demo */}
              <div className="flex items-center justify-center h-64 bg-gradient-to-r from-muted/50 to-accent/50 rounded-lg border-2 border-dashed border-border">
                <div className="text-center">
                  <Clock className="w-16 h-16 text-muted-foreground mx-auto mb-4" />
                  <h4 className="text-xl font-semibold text-foreground mb-2">Interactive Timeline Canvas</h4>
                  <p className="text-muted-foreground">Drag, drop, and organize your timeline events</p>
                  <Button className="mt-4">
                    Start Creating
                  </Button>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </section>

      {/* Footer */}
      <footer className="border-t bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="container mx-auto px-4 py-8">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="flex items-center space-x-2 mb-4 md:mb-0">
              <div className="w-6 h-6 bg-gradient-to-r from-blue-600 to-purple-600 rounded"></div>
              <span className="font-semibold">Timeline Creator</span>
            </div>
            <div className="flex items-center space-x-4 text-sm text-muted-foreground">
              <span>Built with TanStack Start</span>
              <Separator orientation="vertical" className="h-4" />
              <span>Powered by Supabase</span>
              <Separator orientation="vertical" className="h-4" />
              <span>Styled with shadcn/ui</span>
            </div>
          </div>
        </div>
      </footer>
    </div>
  )
}
